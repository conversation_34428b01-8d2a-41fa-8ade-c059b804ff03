# BCTV Web Application - Database Setup Guide

This guide provides step-by-step instructions for setting up the Supabase backend for the BCTV (Beet Curly Top Virus) web application.

## Prerequisites

1. **Supabase Account**: Create a free account at [supabase.com](https://supabase.com)
2. **Node.js 18+**: Required for running the Angular application
3. **Angular CLI 19+**: Install with `npm install -g @angular/cli`

## Step 1: Create Supabase Project

1. Log in to your Supabase dashboard
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: `bctv-management-system`
   - **Database Password**: Choose a strong password (save this!)
   - **Region**: Choose closest to your location
5. Click "Create new project"
6. Wait for the project to be provisioned (2-3 minutes)

## Step 2: Configure Environment Variables

1. In your Supabase project dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (e.g., `https://your-project-id.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

3. Update your environment files:

### `src/environments/environment.ts`
```typescript
export const environment = {
  production: false,
  supabase: {
    url: 'YOUR_SUPABASE_PROJECT_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY'
  },
  maplibre: {
    style: 'https://demotiles.maplibre.org/style.json'
  }
};
```

### `src/environments/environment.prod.ts`
```typescript
export const environment = {
  production: true,
  supabase: {
    url: 'YOUR_SUPABASE_PROJECT_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY'
  },
  maplibre: {
    style: 'https://demotiles.maplibre.org/style.json'
  }
};
```

## Step 3: Enable PostGIS Extension

1. In your Supabase dashboard, go to **Database** > **Extensions**
2. Search for "postgis"
3. Click the toggle to enable PostGIS
4. Wait for the extension to be enabled

## Step 4: Create Database Schema

Go to **SQL Editor** in your Supabase dashboard and run the following SQL commands:

### 1. Create User Profiles Table
```sql
-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Create user profiles table
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  first_name TEXT,
  last_name TEXT,
  organization TEXT,
  phone TEXT,
  role TEXT NOT NULL DEFAULT 'field_worker' CHECK (role IN ('field_worker', 'researcher', 'admin')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Enable RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own profile" ON user_profiles
  FOR INSERT WITH CHECK (auth.uid() = user_id);
```

### 2. Create Observations Table
```sql
-- Create observations table with PostGIS support
CREATE TABLE observations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('host_plant', 'blh_observation', 'bctv_symptoms', 'eradication_effort')),
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  accuracy DECIMAL,
  address TEXT,
  host_plant_data JSONB,
  blh_data JSONB,
  bctv_data JSONB,
  eradication_data JSONB,
  photos TEXT[],
  notes TEXT,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Add spatial column for PostGIS
  location GEOGRAPHY(POINT, 4326) GENERATED ALWAYS AS (ST_Point(longitude, latitude)) STORED
);

-- Create spatial index
CREATE INDEX observations_location_idx ON observations USING GIST (location);

-- Create other indexes
CREATE INDEX observations_user_id_idx ON observations (user_id);
CREATE INDEX observations_type_idx ON observations (type);
CREATE INDEX observations_timestamp_idx ON observations (timestamp);

-- Enable RLS
ALTER TABLE observations ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view all observations" ON observations
  FOR SELECT USING (true);

CREATE POLICY "Users can insert own observations" ON observations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own observations" ON observations
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own observations" ON observations
  FOR DELETE USING (auth.uid() = user_id);
```

### 3. Create Predictions Table
```sql
-- Create predictions table
CREATE TABLE predictions (
  id TEXT PRIMARY KEY,
  location JSONB NOT NULL,
  risk_level TEXT NOT NULL CHECK (risk_level IN ('low', 'medium', 'high', 'very_high')),
  risk_score INTEGER NOT NULL CHECK (risk_score >= 0 AND risk_score <= 100),
  factors JSONB NOT NULL,
  recommendations TEXT[],
  valid_from TIMESTAMP WITH TIME ZONE NOT NULL,
  valid_until TIMESTAMP WITH TIME ZONE NOT NULL,
  confidence DECIMAL(3,2) NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX predictions_risk_level_idx ON predictions (risk_level);
CREATE INDEX predictions_valid_from_idx ON predictions (valid_from);
CREATE INDEX predictions_valid_until_idx ON predictions (valid_until);

-- Enable RLS
ALTER TABLE predictions ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "All users can view predictions" ON predictions
  FOR SELECT USING (true);

CREATE POLICY "Only admins can modify predictions" ON predictions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );
```

## Step 5: Set Up Storage Buckets

1. Go to **Storage** in your Supabase dashboard
2. Create the following buckets:

### Create Buckets
Click "New bucket" for each:

1. **host-plant-observations**
   - Name: `host-plant-observations`
   - Public: `false`
   - File size limit: `10MB`
   - Allowed MIME types: `image/*`

2. **blh-observations**
   - Name: `blh-observations`
   - Public: `false`
   - File size limit: `10MB`
   - Allowed MIME types: `image/*`

3. **bctv-symptoms-observations**
   - Name: `bctv-symptoms-observations`
   - Public: `false`
   - File size limit: `10MB`
   - Allowed MIME types: `image/*`

4. **eradication-observations**
   - Name: `eradication-observations`
   - Public: `false`
   - File size limit: `10MB`
   - Allowed MIME types: `image/*`

### Set Up Storage Policies

For each bucket, go to **Storage** > **Policies** and create these policies:

```sql
-- Allow authenticated users to upload files
CREATE POLICY "Allow authenticated uploads" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id IN ('host-plant-observations', 'blh-observations', 'bctv-symptoms-observations', 'eradication-observations')
    AND auth.role() = 'authenticated'
  );

-- Allow users to view files
CREATE POLICY "Allow authenticated downloads" ON storage.objects
  FOR SELECT USING (
    bucket_id IN ('host-plant-observations', 'blh-observations', 'bctv-symptoms-observations', 'eradication-observations')
    AND auth.role() = 'authenticated'
  );

-- Allow users to delete their own files
CREATE POLICY "Allow users to delete own files" ON storage.objects
  FOR DELETE USING (
    bucket_id IN ('host-plant-observations', 'blh-observations', 'bctv-symptoms-observations', 'eradication-observations')
    AND auth.uid()::text = (storage.foldername(name))[1]
  );
```

## Step 6: Configure Authentication

1. Go to **Authentication** > **Settings**
2. Configure the following:

### Site URL
- Set to your application URL (e.g., `http://localhost:4200` for development)

### Auth Providers
- **Email**: Enable email/password authentication
- **Confirm email**: Enable if you want email verification
- **Secure email change**: Enable for production

### Email Templates
Customize the email templates as needed for your organization.

## Step 7: Create Database Functions (Optional)

Add these helpful functions in the SQL Editor:

```sql
-- Function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (user_id, first_name, last_name)
  VALUES (NEW.id, NEW.raw_user_meta_data->>'first_name', NEW.raw_user_meta_data->>'last_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call the function
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON user_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_observations_updated_at
  BEFORE UPDATE ON observations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_predictions_updated_at
  BEFORE UPDATE ON predictions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Step 8: Test the Setup

1. Start your Angular application:
   ```bash
   ng serve
   ```

2. Navigate to `http://localhost:4200`

3. Try to register a new user account

4. Test the data entry forms to ensure they can save to the database

## Troubleshooting

### Common Issues

1. **Connection Error**: Verify your Supabase URL and anon key are correct
2. **RLS Policies**: Make sure Row Level Security policies are properly configured
3. **PostGIS Extension**: Ensure PostGIS is enabled for spatial queries
4. **Storage Permissions**: Check that storage buckets have proper policies

### Useful SQL Queries for Testing

```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public';

-- View user profiles
SELECT * FROM user_profiles;

-- View observations
SELECT id, type, latitude, longitude, created_at FROM observations;

-- Check storage buckets
SELECT * FROM storage.buckets;
```

## Next Steps

After completing the database setup:

1. **Test all forms**: Try creating observations with each form type
2. **Upload photos**: Test the photo upload functionality
3. **Check data**: Verify data is being saved correctly in the database
4. **Set up monitoring**: Consider setting up database monitoring and backups
5. **Production deployment**: When ready, deploy to production with proper environment variables

## Security Considerations

- **Environment Variables**: Never commit your Supabase keys to version control
- **RLS Policies**: Review and test all Row Level Security policies
- **User Roles**: Implement proper role-based access control
- **Data Validation**: Ensure all data is properly validated on both client and server
- **Backup Strategy**: Set up regular database backups for production

For additional help, refer to the [Supabase Documentation](https://supabase.com/docs) or the project's development team.
