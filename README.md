# BCTV Management System

A comprehensive web application for Beet Curly Top Virus (BCTV) hotspot prediction and eradication support in California agriculture.

## Overview

This Angular-based application provides field workers, researchers, and administrators with tools to:
- Record field observations of host plants and BLH populations
- Document BCTV symptoms and eradication efforts
- Generate AI-powered risk predictions
- Visualize data on interactive maps
- Track and analyze outbreak patterns

## Technology Stack

- **Frontend**: Angular 19 with SSR
- **Backend**: Supabase (PostgreSQL with PostGIS)
- **Mapping**: MapLibre GL JS
- **Storage**: Supabase Storage for photos
- **Authentication**: Supabase Auth

## Features

### ✅ Implemented
- User authentication and role-based access
- Interactive map dashboard with California focus
- Host plant observation forms (10 key BCTV host weeds)
- BLH (Beet Leafhopper) population tracking
- Photo upload with geolocation
- Basic rule-based risk prediction engine
- Mobile-optimized responsive design

### 🚧 In Development
- BCTV symptoms documentation
- Eradication effort tracking
- Advanced map visualizations
- Enhanced prediction algorithms

## Quick Start

### Prerequisites
- Node.js 18+
- Angular CLI 19+
- Supabase account

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd my-angular-app

# Install dependencies
npm install

# Set up environment variables (see Environment Setup below)
# Start development server
ng serve
```

### Environment Setup
Create a Supabase project and update the environment files:

```typescript
// src/environments/environment.ts
export const environment = {
  production: false,
  supabase: {
    url: 'YOUR_SUPABASE_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY'
  },
  maplibre: {
    style: 'https://demotiles.maplibre.org/style.json'
  }
};
```

## Application Structure

```
src/app/
├── core/
│   ├── models/          # Data models and types
│   ├── services/        # Core services (auth, data, etc.)
│   └── guards/          # Route guards
├── features/
│   ├── auth/           # Authentication components
│   ├── dashboard/      # Main dashboard with map
│   ├── data-entry/     # Data collection forms
│   └── predictions/    # Risk prediction views
├── shared/
│   └── components/     # Reusable components
└── environments/       # Environment configurations
```

## Key Components

### Data Models
- **Host Plants**: 10 key BCTV host weeds with density tracking
- **BLH Observations**: Population counts and behavior patterns
- **BCTV Symptoms**: Disease severity and symptom types
- **Eradication Efforts**: Control methods and effectiveness

### Prediction Engine
The application includes a rule-based prediction system that analyzes:
- Host plant density and distribution
- Beet leafhopper populations
- Weather conditions
- Seasonal factors
- Historical outbreak data

### Mobile Features
- GPS integration for accurate location data
- Photo capture and upload
- Offline-capable forms (planned)
- Touch-optimized interface

## User Roles

- **Field Worker**: Data collection and basic reporting
- **Researcher**: Advanced analytics and data export
- **Administrator**: User management and system configuration

## Development

### Running Tests
```bash
# Unit tests
ng test

# E2E tests
ng e2e
```

### Building for Production
```bash
ng build --configuration production
```

### Database Setup
See `DEVELOPMENT_PROGRESS.md` for detailed database schema and setup instructions.

## Contributing

1. Review the `DEVELOPMENT_PROGRESS.md` file for current status
2. Check the roadmap for planned features
3. Follow the established code structure and patterns
4. Ensure mobile responsiveness for all new features
5. Add appropriate tests for new functionality

## License

This project is developed for California agricultural research and management purposes.

## Support

For technical issues or feature requests, please refer to the development roadmap in `DEVELOPMENT_PROGRESS.md`.

---

**Note**: This application is specifically designed for California agricultural use and includes validation for California geographic bounds. The system focuses on the 10 key BCTV host weeds identified as critical for monitoring in California agriculture.
